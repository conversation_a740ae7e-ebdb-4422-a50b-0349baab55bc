package com.example.myapplication;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

import com.android.volley.DefaultRetryPolicy;
import com.android.volley.DefaultRetryPolicy;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.JsonArrayRequest;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class Part5Activity extends AppCompatActivity {

    Button btnTestMethod;
    final String URL = "http://10.0.2.2:8890/siud_loaihang.php"; // For Android Emulator

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_part5);

        Init();
        Act();
    }

    private void Init() {
        btnTestMethod = findViewById(R.id.btnTestMethod);
        btnTestMethod.setText("Test Method");
    }

    private void Act() {
        btnTestMethod.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //TestObjectRequestPOST();
                TestObjectRequestPUTupdate(); // Test UPDATE functionality - now using POST
                //TestStringRequestDELETE();
                //TestGetByID();
                //TestGetAll(); // First test if we can connect and get data
            }
        });
    }

    private void TestObjectRequestPOST() {
        JSONObject object = new JSONObject();
        try {
            object.put("tenloai", "Điện máy");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        JsonObjectRequest request = new JsonObjectRequest(Request.Method.POST,
                URL,
                object,
                new Response.Listener<JSONObject>() {
                    @Override
                    public void onResponse(JSONObject response) {
                        try {
                            String s = response.getString("result");
                            Toast.makeText(Part5Activity.this, s, Toast.LENGTH_SHORT).show();
                        } catch (JSONException e) {
                            throw new RuntimeException(e);
                        }
                    }
                },
                new Response.ErrorListener() {
                    @Override
                    public void onErrorResponse(VolleyError error) {
                        Toast.makeText(Part5Activity.this, error.toString(), Toast.LENGTH_SHORT).show();
                    }
                });

        // Set timeout policy
        request.setRetryPolicy(new DefaultRetryPolicy(
                30000, // 30 seconds timeout
                DefaultRetryPolicy.DEFAULT_MAX_RETRIES,
                DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));

        RequestQueue requestQueue = Volley.newRequestQueue(this);
        requestQueue.add(request);
    }

    private void TestObjectRequestPUTupdate() {
        android.util.Log.d("Part5Test", "=== TESTING UPDATE (POST) ===");
        android.util.Log.d("Part5Test", "URL: " + URL);
        Toast.makeText(this, "Starting UPDATE test...", Toast.LENGTH_SHORT).show();

        JSONObject object = new JSONObject();
        try {
            object.put("idloaihang", 1); // Test with ID 1
            object.put("tenloai", "Updated from Part5 - " + System.currentTimeMillis());
            android.util.Log.d("Part5Test", "JSON payload: " + object.toString());
        } catch (JSONException e) {
            android.util.Log.e("Part5Test", "JSON creation error: " + e.getMessage());
            Toast.makeText(this, "JSON Error: " + e.getMessage(), Toast.LENGTH_LONG).show();
            return;
        }

        JsonObjectRequest request = new JsonObjectRequest(Request.Method.POST, // Changed from PUT to POST
                URL,
                object,
                new Response.Listener<JSONObject>() {
                    @Override
                    public void onResponse(JSONObject response) {
                        android.util.Log.d("Part5Test", "SUCCESS Response: " + response.toString());
                        try {
                            String s = response.getString("result");
                            Toast.makeText(Part5Activity.this, "SUCCESS: " + s, Toast.LENGTH_LONG).show();
                        } catch (JSONException e) {
                            android.util.Log.e("Part5Test", "Response parsing error: " + e.getMessage());
                            Toast.makeText(Part5Activity.this, "Response parsing error", Toast.LENGTH_SHORT).show();
                        }
                    }
                },
                new Response.ErrorListener() {
                    @Override
                    public void onErrorResponse(VolleyError error) {
                        android.util.Log.e("Part5Test", "ERROR: " + error.toString());
                        if (error.networkResponse != null) {
                            android.util.Log.e("Part5Test", "Status code: " + error.networkResponse.statusCode);
                            android.util.Log.e("Part5Test", "Response data: " + new String(error.networkResponse.data));
                        }

                        String errorMessage = "Connection Error";
                        if (error instanceof com.android.volley.TimeoutError) {
                            errorMessage = "Timeout - Check network connection";
                        } else if (error instanceof com.android.volley.NoConnectionError) {
                            errorMessage = "No network connection";
                        } else if (error instanceof com.android.volley.ServerError) {
                            errorMessage = "Server error - Check if server is running";
                        }

                        Toast.makeText(Part5Activity.this, "ERROR: " + errorMessage, Toast.LENGTH_LONG).show();
                    }
                });

        // Set timeout
        request.setRetryPolicy(new com.android.volley.DefaultRetryPolicy(
                10000, // 10 seconds timeout
                1, // no retry
                com.android.volley.DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));

        RequestQueue requestQueue = Volley.newRequestQueue(this);
        requestQueue.add(request);
    }

    private void TestStringRequestDELETE() {
        String url_del = URL + "?IDLOAIHANG=5";
        StringRequest stringRequest = new StringRequest(Request.Method.DELETE,
                url_del,
                new Response.Listener<String>() {
                    @Override
                    public void onResponse(String response) {
                        String s = response.toString();
                        Toast.makeText(Part5Activity.this, s, Toast.LENGTH_SHORT).show();
                    }
                },
                new Response.ErrorListener() {
                    @Override
                    public void onErrorResponse(VolleyError error) {
                        Toast.makeText(Part5Activity.this, error.toString(), Toast.LENGTH_SHORT).show();
                    }
                });

        RequestQueue requestQueue = Volley.newRequestQueue(this);
        requestQueue.add(stringRequest);
    }

    private void TestGetByID() {
        String UrlID = URL + "?ID=3";
        JsonObjectRequest request = new JsonObjectRequest(Request.Method.GET,
                UrlID,
                null,
                new Response.Listener<JSONObject>() {
                    @Override
                    public void onResponse(JSONObject response) {
                        try {
                            String IDLOAIHANG = response.getString("idloaihang");
                            String TENLOAI = response.getString("tenloai");

                            Toast.makeText(Part5Activity.this, IDLOAIHANG + "-" + TENLOAI, Toast.LENGTH_SHORT).show();
                        } catch (JSONException e) {
                            throw new RuntimeException(e);
                        }
                    }
                },
                new Response.ErrorListener() {
                    @Override
                    public void onErrorResponse(VolleyError error) {
                        Toast.makeText(Part5Activity.this, error.toString(), Toast.LENGTH_SHORT).show();
                    }
                });

        RequestQueue requestQueue = Volley.newRequestQueue(this);
        requestQueue.add(request);
    }

    private void TestGetAll() {
        android.util.Log.d("Part5Test", "=== TESTING GET ALL ===");
        android.util.Log.d("Part5Test", "URL: " + URL);

        JsonArrayRequest arrayRequest = new JsonArrayRequest(Request.Method.GET,
                URL,
                null,
                new Response.Listener<JSONArray>() {
                    @Override
                    public void onResponse(JSONArray response) {
                        android.util.Log.d("Part5Test", "SUCCESS - GET ALL Response: " + response.toString());
                        int count = response.length();
                        android.util.Log.d("Part5Test", "Number of items received: " + count);

                        Toast.makeText(Part5Activity.this, "SUCCESS: Got " + count + " items", Toast.LENGTH_LONG).show();

                        // Now test UPDATE after successful GET
                        android.util.Log.d("Part5Test", "Connection successful, now testing UPDATE...");
                        TestObjectRequestPUTupdate();
                    }
                },
                new Response.ErrorListener() {
                    @Override
                    public void onErrorResponse(VolleyError error) {
                        android.util.Log.e("Part5Test", "ERROR - GET ALL: " + error.toString());
                        if (error.networkResponse != null) {
                            android.util.Log.e("Part5Test", "Status code: " + error.networkResponse.statusCode);
                            android.util.Log.e("Part5Test", "Response data: " + new String(error.networkResponse.data));
                        }

                        String errorMessage = "Connection Error";
                        if (error instanceof com.android.volley.TimeoutError) {
                            errorMessage = "Timeout - Check network connection";
                        } else if (error instanceof com.android.volley.NoConnectionError) {
                            errorMessage = "No network connection";
                        } else if (error instanceof com.android.volley.ServerError) {
                            errorMessage = "Server error - Check if server is running";
                        }

                        Toast.makeText(Part5Activity.this, "GET ERROR: " + errorMessage, Toast.LENGTH_LONG).show();
                    }
                });

        // Set timeout
        arrayRequest.setRetryPolicy(new com.android.volley.DefaultRetryPolicy(
                10000, // 10 seconds timeout
                1, // no retry
                com.android.volley.DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));

        RequestQueue requestQueue = Volley.newRequestQueue(this);
        requestQueue.add(arrayRequest);
    }
}
