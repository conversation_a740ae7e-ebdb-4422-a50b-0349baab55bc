package com.example.myapplication;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import java.io.FileNotFoundException;
import java.io.InputStream;

import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;

import org.json.JSONException;
import org.json.JSONObject;

public class IUD_LHActivity extends AppCompatActivity {

    private static final int RQC_Choose_img = 1;
    private static final int RQC_Take_img = 2;
    private static final int REQ_CAMERA_PERMISSION = 100;

    EditText edtTenloai;
    ImageView imgViewHinh;
    Button btnTakeLH, btnChooseLH, btnDeleteLH, btnSaveLH, btnBackLH;
    String ACTION;
    final String URL = "http://********:8890/siud_loaihang.php"; // For Android Emulator
    // Alternative URLs to try if above doesn't work:
    // final String URL = "http://*************:8890/siud_loaihang.php"; // For real device (replace with your IP)
    // final String URL = "http://localhost:8890/siud_loaihang.php"; // localhost
    String ID = "";

    Bitmap BITMAP_IMG;
    boolean imageChangedByUser = false; // Track if user selected new image during update

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_iud_lh);

        Init();
        Act();
    }

    private void Init() {
        edtTenloai = findViewById(R.id.edtTenloai);
        imgViewHinh = findViewById(R.id.imgViewHinh);
        btnBackLH = findViewById(R.id.btnBackLH);
        btnSaveLH = findViewById(R.id.btnSaveLH);
        btnDeleteLH = findViewById(R.id.btnDeleteLH);
        btnChooseLH = findViewById(R.id.btnChooseLH);
        btnTakeLH = findViewById(R.id.btnTakeLH);
        TextView tvTitle = findViewById(R.id.tvTitle);

        Intent intent = getIntent();
        ACTION = intent.getStringExtra("ACTION");
        if (ACTION == null) ACTION = "ADD";
        switch (ACTION) {
            case "ADD":
                btnDeleteLH.setEnabled(false);
                tvTitle.setText("Thêm Loại hàng");
                imageChangedByUser = false; // Reset for ADD mode
                break;
            case "UPDATE":
                btnDeleteLH.setEnabled(true);
                tvTitle.setText("Cập nhật Loại");
                imageChangedByUser = false; // Reset for UPDATE mode - will be set to true if user selects new image

                ID = intent.getStringExtra("ID");
                String tenloai = intent.getStringExtra("TENLOAI");
                String strImg = intent.getStringExtra("HINHANH");

                Log.d("IUD_LH", "=== LOADING UPDATE DATA ===");
                Log.d("IUD_LH", "ID: " + ID);
                Log.d("IUD_LH", "TENLOAI: " + tenloai);
                Log.d("IUD_LH", "HINHANH: " + (strImg == null ? "null" :
                      (strImg.isEmpty() ? "empty" : "length=" + strImg.length())));

                edtTenloai.setText(tenloai);

                if (strImg != null && !strImg.isEmpty()) {
                    Log.d("IUD_LH", "Attempting to decode base64 image...");
                    try {
                        Bitmap bmp = BitmapUtility.getBitmapFromString(strImg);
                        if (bmp != null) {
                            BITMAP_IMG = bmp;
                            imgViewHinh.setImageBitmap(bmp);
                            Log.d("IUD_LH", "Image loaded successfully: " + bmp.getWidth() + "x" + bmp.getHeight());
                        } else {
                            Log.w("IUD_LH", "Failed to decode bitmap from base64");
                            imgViewHinh.setImageResource(android.R.drawable.ic_menu_gallery);
                        }
                    } catch (Exception e) {
                        Log.e("IUD_LH", "Error loading image: " + e.getMessage());
                        imgViewHinh.setImageResource(android.R.drawable.ic_menu_gallery);
                    }
                } else {
                    Log.d("IUD_LH", "No image data provided - using default image");
                    imgViewHinh.setImageResource(android.R.drawable.ic_menu_gallery);
                    BITMAP_IMG = null;
                }
                break;
        }
    }

    private void Act() {
        // Choose image
        btnChooseLH.setOnClickListener(v -> ChooseImage());

        // Take photo
        btnTakeLH.setOnClickListener(v -> TakeImage());

        // Save button
        btnSaveLH.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d("IUD_LH", "Save button clicked. ACTION: " + ACTION);
                Toast.makeText(IUD_LHActivity.this, "Save button clicked! ACTION: " + ACTION, Toast.LENGTH_LONG).show();
                switch (ACTION) {
                    case "ADD":
                        Toast.makeText(IUD_LHActivity.this, "Calling InsertDataLoaiHang()", Toast.LENGTH_SHORT).show();
                        InsertDataLoaiHang();
                        break;
                    case "UPDATE":
                        Toast.makeText(IUD_LHActivity.this, "Calling UpdateDataLoaiHang()", Toast.LENGTH_SHORT).show();
                        UpdateDataLoaiHang();
                        break;
                    default:
                        Log.w("IUD_LH", "Unknown ACTION: " + ACTION + ". Defaulting to ADD");
                        Toast.makeText(IUD_LHActivity.this, "Unknown ACTION: " + ACTION, Toast.LENGTH_SHORT).show();
                        InsertDataLoaiHang();
                        break;
                }
            }
        });

        // Delete logic - implement actual delete functionality
        btnDeleteLH.setOnClickListener(v -> {
            if ("UPDATE".equals(ACTION) && !ID.isEmpty()) {
                DeleteDataLoaiHang();
            } else {
                Toast.makeText(this, "Không thể xóa mục này", Toast.LENGTH_SHORT).show();
            }
        });

        btnBackLH.setOnClickListener(v -> finish());
    }

    private void ChooseImage() {
        Log.d("IUD_LH", "ChooseImage() called");
        Intent intent = new Intent(Intent.ACTION_PICK);
        intent.setType("image/*");
        startActivityForResult(intent, RQC_Choose_img);
    }

    private void TakeImage() {
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{android.Manifest.permission.CAMERA}, REQ_CAMERA_PERMISSION);
            return;
        }
        Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        if (intent.resolveActivity(getPackageManager()) != null) {
            startActivityForResult(intent, RQC_Take_img);
        } else {
            Toast.makeText(this, "Thiết bị không hỗ trợ chụp ảnh", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != RESULT_OK || data == null) return;

        if (requestCode == RQC_Choose_img) {
            Uri uri = data.getData();
            if (uri == null) return;
            try {
                InputStream inputStream = getContentResolver().openInputStream(uri);
                BITMAP_IMG = BitmapFactory.decodeStream(inputStream);
                imgViewHinh.setImageBitmap(BITMAP_IMG);
                imageChangedByUser = true; // Mark that user selected new image
                Log.d("IUD_LH", "User selected new image from gallery");
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
        }

        if (requestCode == RQC_Take_img) {
            BITMAP_IMG = (Bitmap) data.getExtras().get("data");
            imgViewHinh.setImageBitmap(BITMAP_IMG);
            imageChangedByUser = true; // Mark that user took new photo
            Log.d("IUD_LH", "User took new photo");
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQ_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                TakeImage();
            } else {
                Toast.makeText(this, "Bạn cần cấp quyền Camera để chụp ảnh", Toast.LENGTH_SHORT).show();
            }
        }
    }

    // ------------------ INSERT DATA ------------------
    private void InsertDataLoaiHang() {
        String tenloai = edtTenloai.getText().toString().trim();

        // Validation
        if (tenloai.isEmpty()) {
            Toast.makeText(this, "Vui lòng nhập tên loại hàng", Toast.LENGTH_SHORT).show();
            return;
        }

        // Detailed image processing logging
        Log.d("IUD_LH", "=== IMAGE PROCESSING ===");
        Log.d("IUD_LH", "BITMAP_IMG is null: " + (BITMAP_IMG == null));

        String hinhanh = "";
        if (BITMAP_IMG != null) {
            Log.d("IUD_LH", "Bitmap dimensions: " + BITMAP_IMG.getWidth() + "x" + BITMAP_IMG.getHeight());
            Log.d("IUD_LH", "Bitmap config: " + BITMAP_IMG.getConfig());
            Log.d("IUD_LH", "Bitmap byte count: " + BITMAP_IMG.getByteCount());

            try {
                hinhanh = BitmapUtility.getStringFromBitmap(BITMAP_IMG);
                Log.d("IUD_LH", "Base64 conversion successful");
                Log.d("IUD_LH", "Base64 length: " + hinhanh.length());
                Log.d("IUD_LH", "Base64 preview (first 100 chars): " +
                      (hinhanh.length() > 100 ? hinhanh.substring(0, 100) + "..." : hinhanh));
            } catch (Exception e) {
                Log.e("IUD_LH", "Error converting bitmap to base64: " + e.getMessage());
                Toast.makeText(this, "Lỗi xử lý hình ảnh", Toast.LENGTH_SHORT).show();
                return;
            }
        } else {
            Log.w("IUD_LH", "No image selected - sending empty string");
        }

        // Log data being sent
        Log.d("IUD_LH", "=== SENDING DATA ===");
        Log.d("IUD_LH", "- tenloai: " + tenloai);
        Log.d("IUD_LH", "- hinhanh length: " + hinhanh.length());
        Log.d("IUD_LH", "- URL: " + URL);

        JSONObject object = new JSONObject();
        try {
            object.put("tenloai", tenloai);
            object.put("hinhanh", hinhanh);
        } catch (JSONException e) {
            Log.e("IUD_LH", "JSON creation error: " + e.getMessage());
            Toast.makeText(this, "Lỗi tạo dữ liệu JSON", Toast.LENGTH_SHORT).show();
            return;
        }

        JsonObjectRequest request = new JsonObjectRequest(
                Request.Method.POST,
                URL,
                object,
                response -> {
                    Log.d("IUD_LH", "Server response: " + response.toString());
                    try {
                        if (response.has("result")) {
                            String result = response.getString("result");
                            Toast.makeText(IUD_LHActivity.this, result, Toast.LENGTH_SHORT).show();

                            if ("Insert OK".equals(result)) {
                                // Clear form after successful insert
                                edtTenloai.setText("");
                                imgViewHinh.setImageResource(android.R.drawable.ic_menu_gallery);
                                BITMAP_IMG = null;

                                // Go back to main activity
                                finish();
                            }
                        } else if (response.has("error")) {
                            String error = response.getString("error");
                            Toast.makeText(IUD_LHActivity.this, "Lỗi: " + error, Toast.LENGTH_LONG).show();
                        } else {
                            Toast.makeText(IUD_LHActivity.this, "Phản hồi không xác định từ server", Toast.LENGTH_SHORT).show();
                        }
                    } catch (JSONException e) {
                        Log.e("IUD_LH", "JSON parsing error: " + e.getMessage());
                        Toast.makeText(IUD_LHActivity.this, "Lỗi xử lý phản hồi từ server", Toast.LENGTH_SHORT).show();
                    }
                },
                error -> {
                    Log.e("IUD_LH", "Volley error: " + error.toString());
                    if (error.networkResponse != null) {
                        Log.e("IUD_LH", "Status code: " + error.networkResponse.statusCode);
                        Log.e("IUD_LH", "Response data: " + new String(error.networkResponse.data));
                    }

                    String errorMessage = "Lỗi kết nối";
                    if (error instanceof com.android.volley.TimeoutError) {
                        errorMessage = "Timeout - Kiểm tra kết nối mạng";
                    } else if (error instanceof com.android.volley.NoConnectionError) {
                        errorMessage = "Không có kết nối mạng";
                    } else if (error instanceof com.android.volley.ServerError) {
                        errorMessage = "Lỗi server - Kiểm tra server có đang chạy không";
                    } else if (error instanceof com.android.volley.ParseError) {
                        errorMessage = "Lỗi phân tích dữ liệu từ server";
                    }

                    Toast.makeText(IUD_LHActivity.this, errorMessage, Toast.LENGTH_LONG).show();
                });

        // Set timeout
        request.setRetryPolicy(new com.android.volley.DefaultRetryPolicy(
                10000, // 10 seconds timeout
                1, // no retry
                com.android.volley.DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));

        RequestQueue queue = Volley.newRequestQueue(this);
        queue.add(request);
    }

    // ------------------ UPDATE DATA ------------------
    private void UpdateDataLoaiHang() {
        Log.d("IUD_LH", "=== UpdateDataLoaiHang() CALLED ===");
        String tenloai = edtTenloai.getText().toString().trim();
        Log.d("IUD_LH", "UPDATE - Input tenloai: '" + tenloai + "'");
        Log.d("IUD_LH", "UPDATE - Current ID: '" + ID + "'");

        // Validation
        if (tenloai.isEmpty()) {
            Log.w("IUD_LH", "UPDATE - Validation failed: tenloai is empty");
            Toast.makeText(this, "Vui lòng nhập tên loại hàng", Toast.LENGTH_SHORT).show();
            return;
        }

        if (ID.isEmpty()) {
            Log.e("IUD_LH", "UPDATE - Validation failed: ID is empty");
            Toast.makeText(this, "Lỗi: Không có ID để cập nhật", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d("IUD_LH", "UPDATE - Validation passed, proceeding with update...");

        // Detailed image processing logging for UPDATE
        Log.d("IUD_LH", "=== UPDATE IMAGE PROCESSING ===");
        Log.d("IUD_LH", "BITMAP_IMG is null: " + (BITMAP_IMG == null));
        Log.d("IUD_LH", "imageChangedByUser: " + imageChangedByUser);

        String hinhanh = "";

        if (BITMAP_IMG != null && imageChangedByUser) {
            Log.d("IUD_LH", "Processing new image selected by user");
            Log.d("IUD_LH", "Bitmap dimensions: " + BITMAP_IMG.getWidth() + "x" + BITMAP_IMG.getHeight());
            Log.d("IUD_LH", "Bitmap config: " + BITMAP_IMG.getConfig());
            Log.d("IUD_LH", "Bitmap byte count: " + BITMAP_IMG.getByteCount());

            try {
                hinhanh = BitmapUtility.getStringFromBitmap(BITMAP_IMG);
                Log.d("IUD_LH", "Base64 conversion successful for UPDATE");
                Log.d("IUD_LH", "Base64 length: " + hinhanh.length());
                Log.d("IUD_LH", "Base64 preview (first 100 chars): " +
                      (hinhanh.length() > 100 ? hinhanh.substring(0, 100) + "..." : hinhanh));
            } catch (Exception e) {
                Log.e("IUD_LH", "Error converting bitmap to base64 in UPDATE: " + e.getMessage());
                Toast.makeText(this, "Lỗi xử lý hình ảnh", Toast.LENGTH_SHORT).show();
                return;
            }
        } else {
            Log.w("IUD_LH", "No new image selected for UPDATE - will keep existing image");
        }

        // Log data being sent
        Log.d("IUD_LH", "=== UPDATING DATA ===");
        Log.d("IUD_LH", "- ID: " + ID);
        Log.d("IUD_LH", "- tenloai: " + tenloai);
        Log.d("IUD_LH", "- imageChangedByUser: " + imageChangedByUser);
        Log.d("IUD_LH", "- hinhanh length: " + hinhanh.length());
        Log.d("IUD_LH", "- URL: " + URL);

        JSONObject object = new JSONObject();
        try {
            object.put("idloaihang", Integer.parseInt(ID));
            object.put("tenloai", tenloai);
            // Only include hinhanh field if user selected a new image
            if (imageChangedByUser) {
                object.put("hinhanh", hinhanh);
                Log.d("IUD_LH", "Including new image in update");
            } else {
                Log.d("IUD_LH", "Not including image in update - keeping existing image");
            }
            Log.d("IUD_LH", "JSON Payload for update: " + object.toString());
        } catch (JSONException e) {
            Log.e("IUD_LH", "JSON creation error: " + e.getMessage());
            Toast.makeText(this, "Lỗi tạo dữ liệu JSON", Toast.LENGTH_SHORT).show();
            return;
        } catch (NumberFormatException e) {
            Log.e("IUD_LH", "Invalid ID format: " + ID);
            Toast.makeText(this, "Lỗi định dạng ID", Toast.LENGTH_SHORT).show();
            return;
        }

        JsonObjectRequest request = new JsonObjectRequest(
                Request.Method.POST,
                URL,
                object,
                response -> {
                    Log.d("IUD_LH", "UPDATE - Raw response: " + response.toString());
                    try {
                        if (response.has("result")) {
                            String result = response.getString("result");
                            Log.d("IUD_LH", "UPDATE - Parsed result: " + result);
                            Toast.makeText(IUD_LHActivity.this, "Server response: " + result, Toast.LENGTH_LONG).show();

                            if ("Update OK".equals(result)) {
                                finish();
                            }
                        } else if (response.has("error")) {
                            String error = response.getString("error");
                            Toast.makeText(IUD_LHActivity.this, "Lỗi: " + error, Toast.LENGTH_LONG).show();
                        } else {
                            Toast.makeText(IUD_LHActivity.this, "Phản hồi không xác định từ server", Toast.LENGTH_SHORT).show();
                        }
                    } catch (JSONException e) {
                        Log.e("IUD_LH", "JSON parsing error: " + e.getMessage());
                        Toast.makeText(IUD_LHActivity.this, "Lỗi xử lý phản hồi từ server", Toast.LENGTH_SHORT).show();
                    }
                },
                error -> {
                    Log.e("IUD_LH", "Update error: " + error.toString());
                    if (error.networkResponse != null) {
                        Log.e("IUD_LH", "Status code: " + error.networkResponse.statusCode);
                        Log.e("IUD_LH", "Response data: " + new String(error.networkResponse.data));
                    }

                    String errorMessage = "Lỗi cập nhật";
                    if (error instanceof com.android.volley.TimeoutError) {
                        errorMessage = "Timeout - Kiểm tra kết nối mạng";
                    } else if (error instanceof com.android.volley.NoConnectionError) {
                        errorMessage = "Không có kết nối mạng";
                    } else if (error instanceof com.android.volley.ServerError) {
                        errorMessage = "Lỗi server - Kiểm tra server có đang chạy không";
                    }

                    Toast.makeText(IUD_LHActivity.this, errorMessage, Toast.LENGTH_LONG).show();
                });

        // Set timeout
        request.setRetryPolicy(new com.android.volley.DefaultRetryPolicy(
                10000, // 10 seconds timeout
                1, // no retry
                com.android.volley.DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));

        RequestQueue queue = Volley.newRequestQueue(this);
        queue.add(request);
    }

    // ------------------ DELETE DATA ------------------
    private void DeleteDataLoaiHang() {
        if (ID.isEmpty()) {
            Toast.makeText(this, "Lỗi: Không có ID để xóa", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d("IUD_LH", "=== DELETING DATA ===");
        Log.d("IUD_LH", "- ID: " + ID);
        Log.d("IUD_LH", "- URL: " + URL + "?IDLOAIHANG=" + ID);

        // Create DELETE request with ID parameter
        String deleteUrl = URL + "?IDLOAIHANG=" + ID;

        StringRequest request = new StringRequest(
                Request.Method.DELETE,
                deleteUrl,
                response -> {
                    Log.d("IUD_LH", "Delete response: " + response);

                    // Server returns plain text "Delete OK" or "Delete not OK"
                    if ("Delete OK".equals(response.trim())) {
                        Toast.makeText(IUD_LHActivity.this, "Xóa thành công", Toast.LENGTH_SHORT).show();
                        // Go back to main activity
                        finish();
                    } else {
                        Toast.makeText(IUD_LHActivity.this, "Xóa thất bại: " + response, Toast.LENGTH_SHORT).show();
                    }
                },
                error -> {
                    Log.e("IUD_LH", "Delete error: " + error.toString());
                    if (error.networkResponse != null) {
                        Log.e("IUD_LH", "Status code: " + error.networkResponse.statusCode);
                        Log.e("IUD_LH", "Response data: " + new String(error.networkResponse.data));
                    }

                    String errorMessage = "Lỗi xóa dữ liệu";
                    if (error instanceof com.android.volley.TimeoutError) {
                        errorMessage = "Timeout - Kiểm tra kết nối mạng";
                    } else if (error instanceof com.android.volley.NoConnectionError) {
                        errorMessage = "Không có kết nối mạng";
                    } else if (error instanceof com.android.volley.ServerError) {
                        errorMessage = "Lỗi server - Kiểm tra server có đang chạy không";
                    }

                    Toast.makeText(IUD_LHActivity.this, errorMessage, Toast.LENGTH_LONG).show();
                });

        // Set timeout
        request.setRetryPolicy(new com.android.volley.DefaultRetryPolicy(
                10000, // 10 seconds timeout
                1, // no retry
                com.android.volley.DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));

        RequestQueue queue = Volley.newRequestQueue(this);
        queue.add(request);
    }
}