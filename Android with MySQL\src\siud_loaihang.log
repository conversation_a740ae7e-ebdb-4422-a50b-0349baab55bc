2025-06-29 22:05:53 - === NEW REQUEST ===
2025-06-29 22:05:53 - Method: POST
2025-06-29 22:05:53 - URI: /siud_loaihang.php
2025-06-29 22:05:53 - Raw input: {"idloaihang":3,"tenloai":"Test Update POST from PowerShell"}
2025-06-29 22:05:53 - Database connection successful
2025-06-29 22:05:53 - Processing POST request (for Insert/Update)
2025-06-29 22:05:53 - UPDATE: idloaihang = 3
2025-06-29 22:05:53 - UPDATE: tenloai = Test Update POST from PowerShell
2025-06-29 22:05:53 - UPDATE: Updating name only.
2025-06-29 22:05:53 - Update result: SUCCESS
2025-06-29 22:05:53 - === END REQUEST ===

2025-06-29 22:29:48 - === NEW REQUEST ===
2025-06-29 22:29:48 - Method: GET
2025-06-29 22:29:48 - URI: /siud_loaihang.php
2025-06-29 22:29:48 - Raw input: 
2025-06-29 22:29:48 - Database connection successful
2025-06-29 22:29:48 - Processing GET request
2025-06-29 22:29:48 - Getting all items
2025-06-29 22:29:48 - Retrieved 15 items
2025-06-29 22:29:48 - === END REQUEST ===

2025-06-29 22:30:17 - === NEW REQUEST ===
2025-06-29 22:30:17 - Method: GET
2025-06-29 22:30:17 - URI: /siud_loaihang.php
2025-06-29 22:30:17 - Raw input: 
2025-06-29 22:30:17 - Database connection successful
2025-06-29 22:30:17 - Processing GET request
2025-06-29 22:30:17 - Getting all items
2025-06-29 22:30:17 - Retrieved 15 items
2025-06-29 22:30:17 - === END REQUEST ===

2025-06-29 22:31:26 - === NEW REQUEST ===
2025-06-29 22:31:26 - Method: POST
2025-06-29 22:31:26 - URI: /siud_loaihang.php
2025-06-29 22:31:26 - Raw input: {"idloaihang":1,"tenloai":"Test Update Item ID 1"}
2025-06-29 22:31:26 - Database connection successful
2025-06-29 22:31:26 - Processing POST request (for Insert/Update)
2025-06-29 22:31:26 - UPDATE: idloaihang = 1
2025-06-29 22:31:26 - UPDATE: tenloai = Test Update Item ID 1
2025-06-29 22:31:27 - UPDATE: Updating name only.
2025-06-29 22:31:27 - Update result: SUCCESS
2025-06-29 22:31:27 - === END REQUEST ===

2025-06-29 22:54:03 - === NEW REQUEST ===
2025-06-29 22:54:03 - Method: GET
2025-06-29 22:54:03 - URI: /siud_loaihang.php
2025-06-29 22:54:03 - Raw input: 
2025-06-29 22:54:03 - Database connection successful
2025-06-29 22:54:03 - Processing GET request
2025-06-29 22:54:03 - Getting all items
2025-06-29 22:54:03 - Retrieved 15 items
2025-06-29 22:54:03 - === END REQUEST ===

2025-06-29 22:54:14 - === NEW REQUEST ===
2025-06-29 22:54:14 - Method: GET
2025-06-29 22:54:14 - URI: /siud_loaihang.php
2025-06-29 22:54:14 - Raw input: 
2025-06-29 22:54:15 - Database connection successful
2025-06-29 22:54:15 - Processing GET request
2025-06-29 22:54:15 - Getting all items
2025-06-29 22:54:15 - Retrieved 15 items
2025-06-29 22:54:15 - === END REQUEST ===

2025-06-29 22:56:55 - === NEW REQUEST ===
2025-06-29 22:56:55 - Method: GET
2025-06-29 22:56:55 - URI: /siud_loaihang.php
2025-06-29 22:56:56 - Raw input: 
2025-06-29 22:56:56 - Database connection successful
2025-06-29 22:56:56 - Processing GET request
2025-06-29 22:56:56 - Getting all items
2025-06-29 22:56:56 - Retrieved 15 items
2025-06-29 22:56:56 - === END REQUEST ===

